<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="system-detector.js" defer></script>
    <title>AIZOKU - Download</title>
  </head>
  <body class="h-screen w-screen">
    <main
      class="min-h-screen bg-gradient-to-br from-violet-900 to-black text-white flex flex-col items-center justify-center px-6 py-12"
    >
      <div class="text-center max-w-3xl">
        <h1 class="text-5xl md:text-6xl font-extrabold leading-tight mb-6">
          Download <span class="text-violet-400">AIZOKU</span>
        </h1>
        <p class="text-lg md:text-xl text-gray-300 mb-8">
          A powerful, offline AI chat platform. Built for privacy, performance,
          and creativity — all on your machine.
        </p>

        <!-- Auto-detected system download button -->
        <div id="current-system-download" class="mb-12 hidden">
          <div
            class="bg-black/40 p-8 rounded-xl border-2 border-violet-500 flex flex-col items-center"
          >
            <div
              id="current-system-icon"
              class="h-20 w-20 mb-4 text-violet-400"
            ></div>
            <h2 class="text-2xl font-bold mb-2">
              Download for <span id="current-system-name"></span>
            </h2>
            <p id="current-system-specs" class="text-sm text-gray-400 mb-6"></p>
            <a
              id="current-system-link"
              href="#"
              class="bg-violet-600 hover:bg-violet-700 text-white font-semibold px-8 py-3 rounded-lg transition w-full max-w-xs text-center text-lg"
            >
              Download Now
            </a>
            <p class="text-xs text-gray-500 mt-3">
              v0.0.1 • <span id="current-system-size"></span>
            </p>
          </div>
        </div>

        <h3 class="text-xl font-bold mb-6">All Downloads</h3>
        <div
          id="all-downloads"
          class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
        >
      
      </div>

        <div class="mt-8 text-center">
          <h3 class="text-xl font-bold mb-4">System Requirements</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div class="bg-black/20 p-4 rounded-lg">
              <p class="font-semibold mb-2">Windows</p>
              <ul class="text-gray-300 space-y-1">
                <li>Windows 10/11 (64-bit)</li>
                <li>8GB RAM minimum</li>
                <li>2GB free disk space</li>
              </ul>
            </div>
            <div class="bg-black/20 p-4 rounded-lg">
              <p class="font-semibold mb-2">macOS</p>
              <ul class="text-gray-300 space-y-1">
                <li>macOS 11.0 or later</li>
                <li>8GB RAM minimum</li>
                <li>2GB free disk space</li>
              </ul>
            </div>
            <div class="bg-black/20 p-4 rounded-lg">
              <p class="font-semibold mb-2">Linux</p>
              <ul class="text-gray-300 space-y-1">
                <li>Ubuntu 20.04+, Debian 11+</li>
                <li>8GB RAM minimum</li>
                <li>2GB free disk space</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="mt-16 text-sm text-gray-500">
          Made with 💜 using Next.js 15, Ollama, and Tailwind CSS.
        </div>
      </div>
    </main>
  </body>
</html>
